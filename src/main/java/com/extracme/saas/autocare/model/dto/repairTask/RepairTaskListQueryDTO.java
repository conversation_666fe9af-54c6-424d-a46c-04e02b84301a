package com.extracme.saas.autocare.model.dto.repairTask;

import java.util.Date;
import java.util.List;

import com.extracme.saas.autocare.model.dto.base.BasePageDTO;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 维修任务流程查询一览入参
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "维修任务list查询DTO")
public class RepairTaskListQueryDTO extends BasePageDTO {

    @ApiModelProperty(value = "登录机构ID集合")
    private List<String> loginOrgIds;

    @ApiModelProperty(value = "任务所属公司")
    private String orgId;

    @ApiModelProperty(value = "车牌号")
    private String vehicleNo;

    @ApiModelProperty(value = "车架号")
    private String vin;

    @ApiModelProperty(value = "任务编号")
    private String taskNo;

    @ApiModelProperty(value = "流程模板id")
    private Long workflowId;

    @ApiModelProperty(value = "修理类型（1事故维修，2自费维修，3车辆保养）")
    private Integer repairTypeId;

    @ApiModelProperty(value = "修理厂编码")
    private String repairDepotId;

    @ApiModelProperty(value = "修理厂机构id")
    private String repairDepotOrgId;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "任务创建开始时间")
    private Date taskCreateStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "任务创建结束时间")
    private Date taskCreateEndTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "任务流入开始时间")
    private Date taskInflowStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "任务流入结束时间")
    private Date taskInflowEndTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "车辆接收开始时间")
    private Date vehicleReciveStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "车辆接收结束时间")
    private Date vehicleReciveEndTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "车辆验收开始时间")
    private Date vehicleCheckStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "车辆验收结束时间")
    private Date vehicleCheckEndTime;

    @ApiModelProperty(value = "车型id")
    private Long vehicleModelId;

    @ApiModelProperty(value = "业务状态")
    private Integer renttype;

    @ApiModelProperty(value = "实际运营标签")
    private Integer factOperateTag;

    @ApiModelProperty(value = "当前活动节点code")
    private String currentActivityCode;

    @ApiModelProperty(value = "当前活动节点code list")
    private List<String> currentActivityCodeList;

    @ApiModelProperty(value = "状态code")
    private String statusCode;

    @ApiModelProperty(value = "事故编号")
    private String accidentNo;

    @ApiModelProperty(value = "审核级别")
    private Integer advancedAuditLevel;

     @ApiModelProperty(value = "是否超时")
    private Integer isOverTime;
}
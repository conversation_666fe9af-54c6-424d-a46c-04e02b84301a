package com.extracme.saas.autocare.model.dto;

import java.math.BigDecimal;
import java.util.List;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 核损核价审核DTO
 */
@Data
public class LossAssessmentApproveDTO {
    
    /**
     * 任务编号
     */
    @NotBlank(message = "任务编号不能为空")
    @ApiModelProperty(value = "任务编号", required = true, example = "TASK202403150001")
    private String taskNo;

    /**
     * 确认车损类型 1：车辆原因 2：客户原因
     */
    @Min(value = 1, message = "确认车损类型只能是1或2")
    @Max(value = 2, message = "确认车损类型只能是1或2")
    @ApiModelProperty(value = "确认车损类型", notes = "1：车辆原因 2：客户原因", example = "1")
    private Integer confirmCarDamageType;

    /**
     * 损坏部位图片
     */
    @NotNull(message = "损坏部位图片不能为空")
    @Size(min = 1, message = "至少上传一张损坏部位图片")
    @ApiModelProperty(value = "损坏部位图片", notes = "图片URL列表，最多48张", example = "[\"http://example.com/image1.jpg\"]")
    private List<FileDTO> damagedPartPicture;

    /**
     * 损坏部位视频
     */
    @NotNull(message = "损坏部位视频不能为空")
    @ApiModelProperty(value = "损坏部位视频", notes = "视频URL列表", example = "[\"http://example.com/video1.mp4\"]")
    private List<FileDTO> damagedPartVideo;

    /**
     * 总里程数
     */
    @ApiModelProperty(value = "总里程数", example = "50000.00")
    private BigDecimal totalMileage;

    /**
     * 客户是否直付 1:是 2:否
     */
    @NotNull(message = "客户直付标识不能为空")
    @Min(value = 0, message = "客户直付标识只能是1或2")
    @Max(value = 1, message = "客户直付标识只能是1或2")
    @ApiModelProperty(value = "客户是否直付", notes = "1:是 2:否", example = "1")
    private Integer custPaysDirect;

    /**
     * 客户支付金额
     */
    @ApiModelProperty(value = "客户支付金额", example = "1000.00")
    private BigDecimal custAmount;

    /**
     * 客户直付凭证
     */
    @ApiModelProperty(value = "客户直付凭证", notes = "图片URL列表", example = "[\"http://example.com/receipt1.jpg\"]")
    private List<FileDTO> custPicture;

    @ApiModelProperty(value = "用户承担金额", example = "300.00")
    private BigDecimal userAssumedAmount;

    @ApiModelProperty(value = "非用户承担金额", example = "300.00")
    private BigDecimal notUserAssumedAmount;

    /**
     * 预估理赔金额
     */
    @ApiModelProperty(value = "预估理赔金额", example = "2000.00", required = true)
    @DecimalMin(value = "0", message = "预估保险理赔价格不能小于0")
    @Digits(integer = 10, fraction = 2, message = "预估理赔金额格式不正确，整数位最多10位，小数位最多2位")
    private BigDecimal estimatedClaimAmount;

    /**
     * 定损单金额
     */
    @ApiModelProperty(value = "定损单金额", example = "2000.00")
    @DecimalMin(value = "0", message = "定损单金额不能小于0")
    @Digits(integer = 10, fraction = 2, message = "定损单金额格式不正确，整数位最多10位，小数位最多2位")
    private BigDecimal lossOrderAmount;


    private Integer checkFlag;
    /**
     *
     */
    private String approvalRemark;

    @ApiModelProperty(value = "车管核价-换件金额合计", example = "950.00")
    private BigDecimal vehicleReplaceTotalAmount;

    @ApiModelProperty(value = "车管核价-修理金额合计", example = "480.00")
    private BigDecimal vehicleRepairTotalAmount;

    @ApiModelProperty(value = "车管核价-维修总金额", example = "1610.00")
    private BigDecimal vehicleInsuranceTotalAmount;
}

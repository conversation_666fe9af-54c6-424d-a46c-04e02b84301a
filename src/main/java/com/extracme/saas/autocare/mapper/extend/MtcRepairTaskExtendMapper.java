package com.extracme.saas.autocare.mapper.extend;

import java.util.List;

import javax.annotation.Generated;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;

import com.extracme.saas.autocare.annotation.TenantSchema;
import com.extracme.saas.autocare.mapper.base.MtcRepairTaskMapper;
import com.extracme.saas.autocare.model.dto.ActivityCodeResultDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskListQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskWorkflowDTO;
import com.extracme.saas.autocare.model.jingyou.EvalLossInfoAssessment;
import com.extracme.saas.autocare.model.jingyou.EvalLossInfoEvaluate;
import com.extracme.saas.autocare.model.jingyou.FactoryInfo;
import com.extracme.saas.autocare.model.jingyou.LossCoverVehicle;
import com.extracme.saas.autocare.model.jingyou.LossInsured;
import com.extracme.saas.autocare.model.jingyou.LossPolicy;
import com.extracme.saas.autocare.model.jingyou.LossReporting;
import com.extracme.saas.autocare.model.vo.RepairDepotInRepairingVO;
import com.extracme.saas.autocare.model.vo.RepairTaskProcessListVO;
import com.extracme.saas.autocare.model.vo.VehicleRepairRecordVO;


/**
 * 维修任务扩展Mapper
 */
@Mapper
@TenantSchema // 可以指定某些方法不使用租户Schema
public interface MtcRepairTaskExtendMapper extends MtcRepairTaskMapper {

    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: mtc_repair_task")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "RepairTaskProcessListResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "task_no", property = "taskNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "org_name", property = "orgName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vehicle_no", property = "vehicleNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vehicle_model_info", property = "vehicleModelInfo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vin", property = "vin", jdbcType = JdbcType.VARCHAR),
            @Result(column = "insurance_company_name", property = "insuranceCompanyName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "repair_type_id", property = "repairTypeId", jdbcType = JdbcType.INTEGER),
            @Result(column = "repair_type_name", property = "repairTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "repair_grade", property = "repairGrade", jdbcType = JdbcType.VARCHAR),
            @Result(column = "repair_depot_id", property = "repairDepotId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "repair_depot_name", property = "repairDepotName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "task_create_time", property = "taskCreateTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "task_inflow_time", property = "taskInflowTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "vehicle_recive_time", property = "vehicleReciveTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "vehicle_check_time", property = "vehicleCheckTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "instance_id", property = "instanceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "current_activity_code", property = "currentActivityCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "current_activity_name", property = "currentActivityName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "status_code", property = "statusCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "status_name", property = "statusName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "renttype", property = "renttype", jdbcType = JdbcType.INTEGER),
            @Result(column = "fact_operate_tag", property = "factOperateTag", jdbcType = JdbcType.INTEGER),
            @Result(column = "is_used_applets", property = "isUsedApplets", jdbcType = JdbcType.VARCHAR),
            @Result(column = "property_status", property = "propertyStatus", jdbcType = JdbcType.INTEGER),
            @Result(column = "product_line", property = "productLine", jdbcType = JdbcType.INTEGER),
            @Result(column = "sub_product_line", property = "subProductLine", jdbcType = JdbcType.INTEGER)
    })
    List<RepairTaskProcessListVO> selectProcessList(SelectStatementProvider selectStatement);

    /**
     * 通过活动实例表查询维修任务列表
     * 
     * @param queryDTO 查询条件
     * @param tenantId 租户ID
     * @return 维修任务列表
     */
    List<RepairTaskProcessListVO> queryRepairTaskListByActivityInstance(RepairTaskListQueryDTO queryDTO, Integer tenantId);

    /**
     * 通过工作流实例表查询维修任务列表
     * 
     * @param queryDTO 查询条件
     * @param tenantId 租户ID
     * @return 维修任务列表
     */
    List<RepairTaskProcessListVO> queryRepairTaskListByWorkflowInstance(RepairTaskListQueryDTO queryDTO, Integer tenantId);

    /**
     * 查询修理厂在修信息
     * 
     * @param selectStatement 查询语句
     * @return 修理厂在修信息列表
     */
    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: mtc_repair_task")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "RepairTaskWorkflowResult", value = {
        @Result(column = "task_no", property = "taskNo"),
        @Result(column = "vin", property = "vin"),
        @Result(column = "current_activity_code", property = "currentActivityCode"),
        @Result(column = "status_code", property = "statusCode"),
    })
    List<RepairTaskWorkflowDTO> selectRepairTaskWorkflowList(SelectStatementProvider selectStatement);

    /**
     * 查询修理厂在修信息
     * 
     * @param selectStatement 查询语句
     * @return 修理厂在修信息列表
     */
    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: mtc_repair_task")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "RepairDepotInRepairingResult", value = {
        @Result(column = "id", property = "id"),
        @Result(column = "task_no", property = "taskNo"),
        @Result(column = "org_name", property = "orgName"),
        @Result(column = "repair_type_id", property = "repairTypeId"),
        @Result(column = "repair_type_name", property = "repairTypeName"),
        @Result(column = "vin", property = "vin"),
        @Result(column = "vehicle_no", property = "vehicleNo"),
        @Result(column = "vehicle_model_info", property = "vehicleModelInfo"),
        @Result(column = "insurance_company_name", property = "insuranceCompanyName", jdbcType = JdbcType.VARCHAR),
        @Result(column = "vehicle_recive_time", property = "vehicleReciveTime"),
        @Result(column = "expected_repair_complete", property = "expectedRepairComplete"),
        @Result(column = "renttype", property = "renttype"),
        @Result(column = "repair_grade", property = "repairGrade"),
    })
    List<RepairDepotInRepairingVO> selectRepairDepotInRepairingInfo(SelectStatementProvider selectStatement);

    /**
     * 查询评估定损信息
     * 
     * @param selectStatement 查询语句
     * @return 评估定损信息
     */
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "EvalLossInfoAssessmentResult", value = {
            @Result(column = "loss_no", property = "lossNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "report_code", property = "reportCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "plate_no", property = "plateNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "engine_no", property = "engineNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vin_no", property = "vinNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "eval_handler_code", property = "evalHandlerCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "eval_handler_name", property = "evalHandlerName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "enrol_date", property = "enrolDate", jdbcType = JdbcType.TIMESTAMP)
    })
    EvalLossInfoAssessment queryEvalLossInfoAssessment(SelectStatementProvider selectStatement);

    /**
     * 查询修理厂信息
     * 
     * @param selectStatement 查询语句
     * @return 修理厂信息
     */
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "FactoryInfoResult", value = {
            @Result(column = "factory_id", property = "factoryId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "com_code", property = "comCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "com_name", property = "comName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "province_code", property = "provinceCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "province_name", property = "provinceName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "city_code", property = "cityCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "city_name", property = "cityName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "factory_code", property = "factoryCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "factory_name", property = "factoryName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "factory_qualification", property = "factoryQualification", jdbcType = JdbcType.VARCHAR)
    })
    FactoryInfo queryFactoryInfo(SelectStatementProvider selectStatement);

    /**
     * 查询保单信息
     * 
     * @param selectStatement 查询语句
     * @return 保单信息
     */
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "LossPolicyResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.VARCHAR),
            @Result(column = "policy_code", property = "policyCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "report_code", property = "reportCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "insure_bgn_date", property = "insureBgnDate", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "insure_end_date", property = "insureEndDate", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "company_code", property = "companyCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "company_name", property = "companyName", jdbcType = JdbcType.VARCHAR)
    })
    LossPolicy queryLossPolicy(SelectStatementProvider selectStatement);

    /**
     * 查询车辆信息
     * 
     * @param selectStatement 查询语句
     * @return 车辆信息
     */
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "LossCoverVehicleResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.VARCHAR),
            @Result(column = "enrol_date", property = "enrolDate", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "vin_no", property = "vinNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "engine_no", property = "engineNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "plate_num", property = "plateNum", jdbcType = JdbcType.VARCHAR)
    })
    LossCoverVehicle queryLossCoverVehicle(SelectStatementProvider selectStatement);

    /**
     * 查询被保险人信息
     * 
     * @param selectStatement 查询语句
     * @return 被保险人信息
     */
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "LossInsuredResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.VARCHAR),
            @Result(column = "policy_id", property = "policyId", jdbcType = JdbcType.VARCHAR)
    })
    LossInsured queryLossInsured(SelectStatementProvider selectStatement);

    /**
     * 查询报案信息
     * 
     * @param selectStatement 查询语句
     * @return 报案信息
     */
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "LossReportingResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.VARCHAR),
            @Result(column = "report_code", property = "reportCode", jdbcType = JdbcType.VARCHAR)
    })
    LossReporting queryLossReporting(SelectStatementProvider selectStatement);

    /**
     * 查询评估信息
     * 
     * @param selectStatement 查询语句
     * @return 评估信息
     */
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "EvalLossInfoEvaluateResult", value = {
            @Result(column = "dmg_vhcl_id", property = "dmgVhclId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "loss_no", property = "lossNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "report_code", property = "reportCode", jdbcType = JdbcType.VARCHAR)
    })
    EvalLossInfoEvaluate queryEvalLossInfoEvaluate(SelectStatementProvider selectStatement);


    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: mtc_repair_task")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "ActivityCurrentStatusCodeResult", value = {
            @Result(column = "current_status_code", jdbcType = JdbcType.VARCHAR),
    })
    List<String> selectActivityCurrentStatusCodeList(SelectStatementProvider selectStatement);

    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: mtc_repair_task")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "ActivityCodeResult", value = {
            @Result(column = "current_activity_code", property = "currentActivityCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "advanced_audit_level", property = "advancedAuditLevel", jdbcType = JdbcType.INTEGER),
    })
    List<ActivityCodeResultDTO> selectActivityCodeList(SelectStatementProvider selectStatement);

    /**
     * 查询修理厂在修信息
     *
     * @param selectStatement 查询语句
     * @return 修理厂在修信息列表
     */
    @Generated(value = "org.mybatis.generator.api.MyBatisGenerator", comments = "Source Table: mtc_repair_task")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "VehicleRepairRecordResult", value = {
            @Result(column = "id", property = "id"),
            @Result(column = "task_no", property = "taskNo"),
            @Result(column = "repair_type_id", property = "repairTypeId"),
            @Result(column = "repair_type_name", property = "repairTypeName"),
            @Result(column = "repair_depot_name", property = "repairDepotName"),
            @Result(column = "vehicle_insurance_total_amount", property = "vehicleRepairTotalAmount"),
            @Result(column = "task_create_time", property = "taskCreateTime"),
            @Result(column = "task_inflow_time", property = "taskInflowTime"),
            @Result(column = "vehicle_check_time", property = "vehicleCheckTime"),
            @Result(column = "total_mileage", property = "totalMileage"),
    })
    List<VehicleRepairRecordVO> queryVehicleRepairRecord(SelectStatementProvider selectStatement);

    /**
     * 根据条件查询活动当前状态码列表
     * 
     * @param queryDTO 查询条件
     * @param tenantId 租户ID
     * @return 活动当前状态码列表
     */
    List<String> selectActivityCurrentStatusCodeListByCondition(RepairTaskListQueryDTO queryDTO, Integer tenantId);
}

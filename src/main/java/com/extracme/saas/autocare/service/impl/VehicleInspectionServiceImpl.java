package com.extracme.saas.autocare.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.extracme.saas.autocare.enums.ActivityDefinitionEnum;
import com.extracme.saas.autocare.enums.RepairPicTypeEnum;
import com.extracme.saas.autocare.exception.BusinessException;
import com.extracme.saas.autocare.model.dto.FileDTO;
import com.extracme.saas.autocare.model.dto.VehicleInspectionApproveDTO;
import com.extracme.saas.autocare.model.dto.VehicleInspectionRejectDTO;
import com.extracme.saas.autocare.model.dto.workflow.WorkflowProcessDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.entity.WorkflowInstance;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.repository.TableWorkflowInstanceService;
import com.extracme.saas.autocare.service.RepairTaskService;
import com.extracme.saas.autocare.service.VehicleInspectionService;
import com.extracme.saas.autocare.service.WorkflowService;
import com.extracme.saas.autocare.util.SessionUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 车辆验收服务实现类
 * 
 * <AUTHOR> Code
 * @date 2024/05/25
 */
@Slf4j
@Service
public class VehicleInspectionServiceImpl implements VehicleInspectionService {

    @Autowired
    private TableRepairTaskService tableRepairTaskService;
    
    @Autowired
    private TableWorkflowInstanceService tableWorkflowInstanceService;
    
    @Autowired
    private WorkflowService workflowService;

    @Autowired
    private RepairTaskService repairTaskService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approveInspection(VehicleInspectionApproveDTO approveDTO) {
        log.info("车辆验收-验收通过-开始，任务编号：{}", approveDTO.getTaskNo());
        
        try {
            // 1. 获取维修任务详情
            MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(approveDTO.getTaskNo());
            if (repairTask == null) {
                throw new BusinessException("维修任务不存在");
            }
            
            // 2. 获取工作流实例
            WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(approveDTO.getTaskNo());
            if (workflowInstance == null) {
                throw new BusinessException("工作流实例不存在");
            }
            
            // 3. 检查当前环节是否处于验收环节
            if (!ActivityDefinitionEnum.QUALITY_INSPECTION.getCode().equals(workflowInstance.getCurrentActivityCode())) {
                throw new BusinessException("当前不处于验收环节，请刷新页面");
            }
            
            // 4. 更新维修任务验收信息
            MtcRepairTask updateTask = new MtcRepairTask();
            updateTask.setId(repairTask.getId());
            updateTask.setVehicleCheckTime(new Date());
            int updateResult = tableRepairTaskService.updateSelectiveById(updateTask);
            if (updateResult < 1) {
                throw new BusinessException("更新维修任务验收信息失败");
            }
            
            // 5. 处理验收图片和视频
            Map<Integer, List<FileDTO>> mediaTypeMap = new HashMap<>();
            mediaTypeMap.put(RepairPicTypeEnum.REPAIR_PICTURE.getTypeId(), approveDTO.getRepairPicture()); // 验收图片
            mediaTypeMap.put(RepairPicTypeEnum.CHECK_VIDEO.getTypeId(), approveDTO.getCheckVideo()); // 验收视频
            
            // 调用RepairTaskServiceImpl中的公共方法处理图片和视频
            repairTaskService.processMediaFiles(approveDTO.getTaskNo(), mediaTypeMap, SessionUtils.getUsername());
            
            // 6. 调用工作流服务处理节点
            WorkflowProcessDTO processDTO = new WorkflowProcessDTO();
            processDTO.setTriggerEvent("APPROVE_INSPECTION");
            processDTO.setActivityCode(workflowInstance.getCurrentActivityCode());
            
            try {
                workflowService.processNode(workflowInstance.getId(), processDTO);
                log.info("车辆验收通过工作流处理成功，任务ID：{}", repairTask.getId());
            } catch (Exception e) {
                log.error("车辆验收通过工作流处理失败: {}", e.getMessage(), e);
                throw new BusinessException("车辆验收通过工作流处理失败: " + e.getMessage());
            }
            
            log.info("车辆验收通过完成，任务ID：{}", repairTask.getId());
        } catch (Exception e) {
            log.error("车辆验收通过异常，任务编号：{}，异常信息：{}", approveDTO.getTaskNo(), e.getMessage(), e);
            throw new BusinessException("车辆验收通过异常：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rejectInspection(VehicleInspectionRejectDTO rejectDTO) {
        log.info("车辆验收-验收不通过-开始，任务编号：{}", rejectDTO.getTaskNo());
        
        try {
            // 1. 获取维修任务详情
            MtcRepairTask repairTask = tableRepairTaskService.selectByTaskNo(rejectDTO.getTaskNo());
            if (repairTask == null) {
                throw new BusinessException("维修任务不存在");
            }
            
            // 2. 获取工作流实例
            WorkflowInstance workflowInstance = tableWorkflowInstanceService.selectByBusinessId(rejectDTO.getTaskNo());
            if (workflowInstance == null) {
                throw new BusinessException("工作流实例不存在");
            }
            
            // 3. 检查当前环节是否处于验收环节
            if (!ActivityDefinitionEnum.QUALITY_INSPECTION.getCode().equals(workflowInstance.getCurrentActivityCode())) {
                throw new BusinessException("当前不处于验收环节，请刷新页面");
            }

            // 4. 处理验收图片和视频
            Map<Integer, List<FileDTO>> mediaTypeMap = new HashMap<>();
            mediaTypeMap.put(RepairPicTypeEnum.REPAIR_PICTURE.getTypeId(), rejectDTO.getRepairPicture()); // 验收图片
            mediaTypeMap.put(RepairPicTypeEnum.CHECK_VIDEO.getTypeId(), rejectDTO.getCheckVideo()); // 验收视频
            // 调用RepairTaskServiceImpl中的公共方法处理图片和视频
            repairTaskService.processMediaFiles(rejectDTO.getTaskNo(), mediaTypeMap, SessionUtils.getUsername());

            // 5. 调用工作流服务，触发"REJECT_INSPECTION"事件
            WorkflowProcessDTO processDTO = new WorkflowProcessDTO();
            processDTO.setTriggerEvent("REJECT_INSPECTION");
            processDTO.setActivityCode(workflowInstance.getCurrentActivityCode());

            try {
                workflowService.processNode(workflowInstance.getId(), processDTO);
                log.info("车辆验收不通过工作流处理成功，任务ID：{}", repairTask.getId());
            } catch (Exception e) {
                log.error("车辆验收不通过工作流处理失败: {}", e.getMessage(), e);
                throw new BusinessException("车辆验收不通过工作流处理失败: " + e.getMessage());
            }
            
            log.info("车辆验收不通过完成，任务ID：{}", repairTask.getId());
        } catch (Exception e) {
            log.error("车辆验收不通过异常，任务编号：{}，异常信息：{}", rejectDTO.getTaskNo(), e.getMessage(), e);
            throw new BusinessException("车辆验收不通过异常：" + e.getMessage());
        }
    }
}

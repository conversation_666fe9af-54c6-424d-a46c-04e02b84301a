package com.extracme.saas.autocare.repository.impl;

import static com.extracme.saas.autocare.mapper.base.MtcRepairDepotInfoDynamicSqlSupport.mtcRepairDepotInfo;
import static com.extracme.saas.autocare.mapper.base.MtcRepairTaskDynamicSqlSupport.id;
import static com.extracme.saas.autocare.mapper.base.MtcRepairTaskDynamicSqlSupport.mtcRepairTask;
import static com.extracme.saas.autocare.mapper.base.MtcRepairTaskLeavingFactoryDynamicSqlSupport.mtcRepairTaskLeavingFactory;
import static com.extracme.saas.autocare.mapper.base.WorkflowInstanceDynamicSqlSupport.workflowInstance;
import static org.mybatis.dynamic.sql.SqlBuilder.and;
import static org.mybatis.dynamic.sql.SqlBuilder.equalTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isBetweenWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThanOrEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThanWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isInWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isLikeWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isNotEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isNotIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isNotInWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isNotNull;
import static org.mybatis.dynamic.sql.SqlBuilder.max;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.extracme.saas.autocare.enums.ActivityDefinitionEnum;
import com.extracme.saas.autocare.enums.ActivityStatusEnum;
import com.extracme.saas.autocare.mapper.base.MtcRepairTaskDynamicSqlSupport;
import com.extracme.saas.autocare.mapper.extend.MtcRepairTaskExtendMapper;
import com.extracme.saas.autocare.model.dto.ActivityCodeResultDTO;
import com.extracme.saas.autocare.model.dto.GetRepairNumDTO;
import com.extracme.saas.autocare.model.dto.RepairCustInfoDTO;
import com.extracme.saas.autocare.model.dto.RepairDepotQueryDTO;
import com.extracme.saas.autocare.model.dto.VehicleRepairRecordQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskListQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskProcessQueryDTO;
import com.extracme.saas.autocare.model.dto.repairTask.RepairTaskWorkflowDTO;
import com.extracme.saas.autocare.model.entity.MtcRepairTask;
import com.extracme.saas.autocare.model.vo.RepairDepotInRepairingVO;
import com.extracme.saas.autocare.model.vo.RepairTaskProcessListVO;
import com.extracme.saas.autocare.model.vo.VehicleRepairRecordVO;
import com.extracme.saas.autocare.repository.TableRepairTaskService;
import com.extracme.saas.autocare.util.DateTimeUtils;
import com.extracme.saas.autocare.util.SessionUtils;

/**
 * 维修任务表数据访问服务实现
 */
@Repository
public class TableRepairTaskServiceImpl implements TableRepairTaskService {

    @Autowired
    private MtcRepairTaskExtendMapper mtcRepairTaskMapper;

    @Override
    public MtcRepairTask insert(MtcRepairTask record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return insert(record, operator);
    }

    @Override
    public MtcRepairTask insert(MtcRepairTask record, String operator) {
        if (record == null) {
            return null;
        }
        Date now = new Date();
        record.setCreatedTime(now);
        record.setCreateBy(operator);
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        mtcRepairTaskMapper.insertSelective(record);
        return record;
    }

    @Override
    public int updateSelectiveById(MtcRepairTask record) {
        // 从会话中获取当前用户作为操作人
        String operator = SessionUtils.getLoginUser().getUsername();
        // 调用带操作人参数的方法
        return updateSelectiveById(record, operator);
    }

    @Override
    public int updateSelectiveById(MtcRepairTask record, String operator) {
        if (record == null) {
            return 0;
        }
        Date now = new Date();
        record.setUpdatedTime(now);
        record.setUpdateBy(operator);
        return mtcRepairTaskMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public MtcRepairTask selectById(Long id) {
        if (id == null) {
            return null;
        }
        return mtcRepairTaskMapper.selectByPrimaryKey(id).orElse(null);
    }

    @Override
    public MtcRepairTask selectByTaskNo(String taskNo) {
        SelectDSLCompleter completer = c -> c.where(MtcRepairTaskDynamicSqlSupport.taskNo, isEqualTo(taskNo));
        Optional<MtcRepairTask> optionalTask = mtcRepairTaskMapper.selectOne(completer);
        return optionalTask.orElse(null);
    }

    @Override
    public List<MtcRepairTask> selectByVin(String vin) {
        SelectDSLCompleter completer = c -> c.where(MtcRepairTaskDynamicSqlSupport.vin, isEqualTo(vin))
                .orderBy(id.descending());
        return mtcRepairTaskMapper.select(completer);
    }

    @Override
    public List<RepairTaskProcessListVO> queryProcessList(RepairTaskProcessQueryDTO queryDTO) {
        SelectStatementProvider selectStatement = select(
                mtcRepairTask.id,
                mtcRepairTask.taskNo,
                mtcRepairTask.orgId,
                mtcRepairTask.orgName,
                mtcRepairTask.vehicleNo,
                mtcRepairTask.vehicleModelSeq,
                mtcRepairTask.vehicleModelInfo,
                mtcRepairTask.vin,
                mtcRepairTask.insuranceCompanyName,
                mtcRepairTask.repairTypeId,
                mtcRepairTask.repairTypeName,
                mtcRepairTask.repairGrade,
                mtcRepairTask.repairDepotId,
                mtcRepairTask.repairDepotName,
                mtcRepairDepotInfo.repairDepotType,
                mtcRepairTask.taskCreateTime,
                mtcRepairTask.taskInflowTime,
                mtcRepairTask.vehicleReciveTime,
                mtcRepairTask.vehicleCheckTime,
                mtcRepairTask.renttype,
                mtcRepairTask.factOperateTag,
                mtcRepairTask.isUsedApplets,
                mtcRepairTask.propertyStatus,
                mtcRepairTask.productLine,
                mtcRepairTask.subProductLine,
                mtcRepairTask.createBy,
                mtcRepairTask.createdTime,
                mtcRepairTask.updateBy,
                mtcRepairTask.updatedTime,
                workflowInstance.id.as("instance_id"),
                workflowInstance.currentActivityCode,
                workflowInstance.statusCode)
                .from(mtcRepairTask)
                .leftJoin(workflowInstance).on(workflowInstance.businessId, equalTo(mtcRepairTask.taskNo))
                .leftJoin(mtcRepairDepotInfo).on(mtcRepairTask.repairDepotId, equalTo(mtcRepairDepotInfo.repairDepotId))
                .where()
                .and(mtcRepairTask.orgId, isInWhenPresent(queryDTO.getLoginOrgIds()))
                .and(mtcRepairTask.vin, isEqualToWhenPresent(queryDTO.getVin()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.vehicleNo,
                        isEqualToWhenPresent(queryDTO.getVehicleNo()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.taskNo, isEqualToWhenPresent(queryDTO.getTaskNo()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.repairTypeId, isEqualToWhenPresent(queryDTO.getRepairTypeId()))
                .and(mtcRepairTask.repairDepotId,
                        isEqualToWhenPresent(queryDTO.getRepairDepotId()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.repairDepotOrgId, isEqualToWhenPresent(queryDTO.getRepairDepotOrgId()))
                .and(mtcRepairTask.vehicleModelSeq, isEqualToWhenPresent(queryDTO.getVehicleModelId()))
                .and(mtcRepairTask.renttype, isEqualToWhenPresent(queryDTO.getRenttype()))
                .and(mtcRepairTask.orgId, isEqualToWhenPresent(queryDTO.getOrgId()).filter(StringUtils::isNotBlank))
                .and(workflowInstance.currentActivityCode,
                        isEqualToWhenPresent(queryDTO.getCurrentActivityCode()).filter(StringUtils::isNotBlank))
                .and(mtcRepairDepotInfo.repairDepotType, isEqualToWhenPresent(queryDTO.getRepairDepotType()))
                .and(mtcRepairTask.taskCreateTime,
                        isBetweenWhenPresent(queryDTO.getTaskCreateStartTime())
                                .and(DateTimeUtils.addOneDay(queryDTO.getTaskCreateEndTime())))
                .and(mtcRepairTask.taskInflowTime,
                        isBetweenWhenPresent(queryDTO.getTaskInflowStartTime())
                                .and(DateTimeUtils.addOneDay(queryDTO.getTaskInflowEndTime())))
                .and(mtcRepairTask.vehicleReciveTime,
                        isBetweenWhenPresent(queryDTO.getVehicleReciveStartTime())
                                .and(DateTimeUtils.addOneDay(queryDTO.getVehicleReciveEndTime())))
                .and(mtcRepairTask.vehicleCheckTime,
                        isBetweenWhenPresent(queryDTO.getVehicleCheckStartTime())
                                .and(DateTimeUtils.addOneDay(queryDTO.getVehicleCheckEndTime())))
                .orderBy(mtcRepairTask.createdTime.descending(), mtcRepairTask.id)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mtcRepairTaskMapper.selectProcessList(selectStatement);
    }

    @Override
    public List<RepairTaskProcessListVO> exportProcessListWithPageSize(long lastId, RepairTaskProcessQueryDTO queryDTO,
            int pageSize) {
        SelectStatementProvider selectStatement = select(
                mtcRepairTask.id,
                mtcRepairTask.taskNo,
                mtcRepairTask.orgId,
                mtcRepairTask.orgName,
                mtcRepairTask.vehicleNo,
                mtcRepairTask.vehicleModelSeq,
                mtcRepairTask.vehicleModelInfo,
                mtcRepairTask.vin,
                mtcRepairTask.insuranceCompanyName,
                mtcRepairTask.repairTypeId,
                mtcRepairTask.repairTypeName,
                mtcRepairTask.repairGrade,
                mtcRepairTask.repairDepotId,
                mtcRepairTask.repairDepotName,
                mtcRepairDepotInfo.repairDepotType,
                mtcRepairTask.taskCreateTime,
                mtcRepairTask.taskInflowTime,
                mtcRepairTask.vehicleReciveTime,
                mtcRepairTask.vehicleCheckTime,
                mtcRepairTask.renttype,
                mtcRepairTask.factOperateTag,
                mtcRepairTask.isUsedApplets,
                mtcRepairTask.propertyStatus,
                mtcRepairTask.productLine,
                mtcRepairTask.subProductLine,
                mtcRepairTask.createBy,
                mtcRepairTask.createdTime,
                mtcRepairTask.updateBy,
                mtcRepairTask.updatedTime,
                workflowInstance.id.as("instance_id"),
                workflowInstance.currentActivityCode,
                workflowInstance.statusCode)
                .from(mtcRepairTask)
                .leftJoin(workflowInstance).on(workflowInstance.businessId, equalTo(mtcRepairTask.taskNo))
                .leftJoin(mtcRepairDepotInfo).on(mtcRepairTask.repairDepotId, equalTo(mtcRepairDepotInfo.repairDepotId))
                .where()
                .and(mtcRepairTask.orgId, isInWhenPresent(queryDTO.getLoginOrgIds()))
                .and(mtcRepairTask.vin, isEqualToWhenPresent(queryDTO.getVin()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.vehicleNo,
                        isEqualToWhenPresent(queryDTO.getVehicleNo()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.taskNo, isEqualToWhenPresent(queryDTO.getTaskNo()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.repairTypeId, isEqualToWhenPresent(queryDTO.getRepairTypeId()))
                .and(mtcRepairTask.repairDepotId,
                        isEqualToWhenPresent(queryDTO.getRepairDepotId()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.repairDepotOrgId, isEqualToWhenPresent(queryDTO.getRepairDepotOrgId()))
                .and(mtcRepairTask.vehicleModelSeq, isEqualToWhenPresent(queryDTO.getVehicleModelId()))
                .and(mtcRepairTask.renttype, isEqualToWhenPresent(queryDTO.getRenttype()))
                .and(mtcRepairTask.orgId, isEqualToWhenPresent(queryDTO.getOrgId()).filter(StringUtils::isNotBlank))
                .and(workflowInstance.currentActivityCode,
                        isEqualToWhenPresent(queryDTO.getCurrentActivityCode()).filter(StringUtils::isNotBlank))
                .and(mtcRepairDepotInfo.repairDepotType, isEqualToWhenPresent(queryDTO.getRepairDepotType()))
                .and(mtcRepairTask.taskCreateTime,
                        isBetweenWhenPresent(queryDTO.getTaskCreateStartTime())
                                .and(DateTimeUtils.addOneDay(queryDTO.getTaskCreateEndTime())))
                .and(mtcRepairTask.taskInflowTime,
                        isBetweenWhenPresent(queryDTO.getTaskInflowStartTime())
                                .and(DateTimeUtils.addOneDay(queryDTO.getTaskInflowEndTime())))
                .and(mtcRepairTask.vehicleReciveTime,
                        isBetweenWhenPresent(queryDTO.getVehicleReciveStartTime())
                                .and(DateTimeUtils.addOneDay(queryDTO.getVehicleReciveEndTime())))
                .and(mtcRepairTask.vehicleCheckTime,
                        isBetweenWhenPresent(queryDTO.getVehicleCheckStartTime())
                                .and(DateTimeUtils.addOneDay(queryDTO.getVehicleCheckEndTime())))
                .and(mtcRepairTask.id, isGreaterThanWhenPresent(lastId))
                .orderBy(mtcRepairTask.createdTime.descending(), mtcRepairTask.id)
                .limit(pageSize)
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mtcRepairTaskMapper.selectProcessList(selectStatement);
    }

    @Override
    public List<RepairTaskProcessListVO> queryRepairTaskListByActivityInstance(RepairTaskListQueryDTO queryDTO,
            Integer tenantId) {
        // 直接调用Mapper中的XML查询方法，不再使用动态SQL
        return mtcRepairTaskMapper.queryRepairTaskListByActivityInstance(queryDTO, tenantId);
    }

    @Override
    public List<RepairTaskProcessListVO> queryRepairTaskListByWorkflowInstance(RepairTaskListQueryDTO queryDTO,
            Integer tenantId) {
        // 直接调用Mapper中的XML查询方法，不再使用动态SQL
        return mtcRepairTaskMapper.queryRepairTaskListByWorkflowInstance(queryDTO, tenantId);
    }

    @Override
    public String getOverTime(Long id) {
        // 实现获取任务是否超时的逻辑
        // 这里需要根据实际业务逻辑实现，例如：
        // 1. 获取任务的预计完成时间和当前时间
        // 2. 比较两个时间，判断是否超时
        // 3. 返回超时状态：0表示超时，1表示未超时

        // 示例实现，实际应根据业务需求调整
        MtcRepairTask task = selectById(id);
        if (task == null) {
            return "1"; // 默认不超时
        }

        // 获取任务的预计修理天数和送修时间
        // 假设MtcRepairTask中的expectedRepairDays字段是String类型
        String expectedRepairDaysStr = String.valueOf(task.getExpectedRepairDays());
        Date sendRepairTime = task.getSendRepairTime();

        if (StringUtils.isBlank(expectedRepairDaysStr) || sendRepairTime == null) {
            return "1"; // 如果没有预计修理天数或送修时间，默认不超时
        }

        try {
            int days = Integer.parseInt(expectedRepairDaysStr);
            Date now = new Date();

            // 计算预计完成时间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(sendRepairTime);
            calendar.add(Calendar.DAY_OF_MONTH, days);
            Date expectedCompleteTime = calendar.getTime();

            // 判断是否超时
            if (now.after(expectedCompleteTime)) {
                return "0"; // 超时
            } else {
                return "1"; // 未超时
            }
        } catch (NumberFormatException e) {
            return "1"; // 如果预计修理天数格式不正确，默认不超时
        }
    }

    /**
     * 清除任务核损核价占有人
     * 将维修任务的核损核价任务占有人字段设置为null
     *
     * @param taskId   维修任务ID
     * @param operator 操作人
     * @return 更新结果，返回受影响的行数
     */
    @Override
    public int clearOwner(Long taskId, String operator) {
        if (taskId == null) {
            return 0;
        }

        if (StringUtils.isBlank(operator)) {
            return 0;
        }

        Date now = new Date();

        // 使用MyBatis动态SQL构建更新语句
        return mtcRepairTaskMapper
                .update(c -> c.set(MtcRepairTaskDynamicSqlSupport.verificationLossTaskOperId).equalToNull()
                        .set(MtcRepairTaskDynamicSqlSupport.updateBy).equalTo(operator)
                        .set(MtcRepairTaskDynamicSqlSupport.updatedTime).equalTo(now)
                        .where(MtcRepairTaskDynamicSqlSupport.id, isEqualTo(taskId)));
    }

    @Override
    public List<RepairTaskWorkflowDTO> getRepairNum(GetRepairNumDTO getRepairNumDTO) {
        // 当前维修数量
        SelectStatementProvider currentRepairCountStmt = select(
                mtcRepairTask.taskNo,
                mtcRepairTask.vin,
                workflowInstance.currentActivityCode,
                workflowInstance.statusCode)
                .from(mtcRepairTask)
                .leftJoin(workflowInstance).on(workflowInstance.businessId, equalTo(mtcRepairTask.taskNo))
                .where()
                .and(mtcRepairTask.repairDepotOrgId, isInWhenPresent(getRepairNumDTO.getLoginOrgIds()))
                .and(mtcRepairTask.repairDepotOrgId, isEqualToWhenPresent(getRepairNumDTO.getOrgId()))
                .and(mtcRepairTask.repairDepotId, isEqualToWhenPresent(getRepairNumDTO.getRepairDepotId()))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mtcRepairTaskMapper.selectRepairTaskWorkflowList(currentRepairCountStmt);
    }

    @Override
    public List<RepairTaskWorkflowDTO> getCurrentRepairNum(GetRepairNumDTO getRepairNumDTO) {
        // 当前维修数量
        SelectStatementProvider currentRepairCountStmt = select(
                mtcRepairTask.taskNo,
                mtcRepairTask.vin,
                workflowInstance.currentActivityCode,
                workflowInstance.statusCode)
                .from(mtcRepairTask)
                .leftJoin(workflowInstance).on(workflowInstance.businessId, equalTo(mtcRepairTask.taskNo))
                .where()
                .and(mtcRepairTask.orgId, isInWhenPresent(getRepairNumDTO.getLoginOrgIds()))
                .and(mtcRepairTask.orgId, isEqualToWhenPresent(getRepairNumDTO.getOrgId()))
                .and(mtcRepairTask.repairDepotId, isEqualToWhenPresent(getRepairNumDTO.getRepairDepotId()))
                // 在修任务
                .and(workflowInstance.statusCode, isNotInWhenPresent(Arrays.asList("CLOSED", "COMPLETED")))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mtcRepairTaskMapper.selectRepairTaskWorkflowList(currentRepairCountStmt);
    }

    @Override
    public List<RepairTaskWorkflowDTO> getTodayRepairNum(GetRepairNumDTO getRepairNumDTO) {
        LocalDate today = LocalDate.now();
        Date todayStart = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
        // 当日维修数量
        SelectStatementProvider currentRepairCountStmt = select(
                mtcRepairTask.taskNo,
                mtcRepairTask.vin,
                workflowInstance.currentActivityCode,
                workflowInstance.statusCode)
                .from(mtcRepairTask)
                .leftJoin(workflowInstance).on(workflowInstance.businessId, equalTo(mtcRepairTask.taskNo))
                .where()
                .and(mtcRepairTask.orgId, isInWhenPresent(getRepairNumDTO.getLoginOrgIds()))
                .and(mtcRepairTask.orgId, isEqualToWhenPresent(getRepairNumDTO.getOrgId()))
                .and(mtcRepairTask.repairDepotId, isEqualToWhenPresent(getRepairNumDTO.getRepairDepotId()))
                // 当日创建的任务
                .and(mtcRepairTask.createdTime, isGreaterThanOrEqualToWhenPresent(todayStart))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mtcRepairTaskMapper.selectRepairTaskWorkflowList(currentRepairCountStmt);
    }

    @Override
    public List<RepairTaskWorkflowDTO> getTodayCompleteNum(GetRepairNumDTO getRepairNumDTO) {
        LocalDate today = LocalDate.now();
        Date todayStart = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
        // 当日维修数量
        SelectStatementProvider currentRepairCountStmt = select(
                mtcRepairTask.taskNo,
                mtcRepairTask.vin,
                workflowInstance.currentActivityCode,
                workflowInstance.statusCode,
                mtcRepairTaskLeavingFactory.leavingStatus)
                .from(mtcRepairTask)
                .leftJoin(workflowInstance).on(workflowInstance.businessId, equalTo(mtcRepairTask.taskNo))
                .leftJoin(mtcRepairTaskLeavingFactory)
                .on(mtcRepairTaskLeavingFactory.taskNo, equalTo(mtcRepairTask.taskNo))
                .where()
                .and(mtcRepairTaskLeavingFactory.id, isEqualTo(
                        select(max(mtcRepairTaskLeavingFactory.id))
                                .from(mtcRepairTaskLeavingFactory)
                                .where(mtcRepairTaskLeavingFactory.taskNo, isEqualTo(mtcRepairTask.taskNo))))
                .and(mtcRepairTask.orgId, isInWhenPresent(getRepairNumDTO.getLoginOrgIds()))
                .and(mtcRepairTask.orgId, isEqualToWhenPresent(getRepairNumDTO.getOrgId()))
                .and(mtcRepairTask.repairDepotId, isEqualToWhenPresent(getRepairNumDTO.getRepairDepotId()))
                // 当日验收通过的任务
                .and(mtcRepairTask.vehicleCheckTime, isGreaterThanOrEqualToWhenPresent(todayStart))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mtcRepairTaskMapper.selectRepairTaskWorkflowList(currentRepairCountStmt);
    }

    @Override
    public List<RepairDepotInRepairingVO> queryRepairDepotInRepairingInfo(RepairDepotQueryDTO queryDTO) {
        SelectStatementProvider selectStatement = select(
                mtcRepairTask.id,
                mtcRepairTask.taskNo,
                mtcRepairTask.orgName,
                mtcRepairTask.repairTypeId,
                mtcRepairTask.repairTypeName,
                mtcRepairTask.vin,
                mtcRepairTask.vehicleNo,
                mtcRepairTask.vehicleModelSeq,
                mtcRepairTask.vehicleModelInfo,
                mtcRepairTask.vehicleReciveTime,
                mtcRepairTask.insuranceCompanyName,
                mtcRepairTask.repairGrade,
                mtcRepairTask.vehicleReciveTime,
                mtcRepairTask.expectedRepairComplete,
                mtcRepairTask.renttype)
                .from(mtcRepairTask)
                .leftJoin(workflowInstance).on(workflowInstance.businessId, equalTo(mtcRepairTask.taskNo))
                .where()
                .and(mtcRepairTask.orgId, isInWhenPresent(queryDTO.getLoginOrgIds()))
                .and(mtcRepairTask.orgId, isEqualToWhenPresent(queryDTO.getOrgId()))
                .and(mtcRepairTask.repairDepotId, isEqualToWhenPresent(queryDTO.getRepairDepotId()))
                .orderBy(mtcRepairTask.updatedTime.descending())
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return mtcRepairTaskMapper.selectRepairDepotInRepairingInfo(selectStatement);
    }

    @Override
    public Map<String, Long> countByToActivityCodeGroupByStatusCode(RepairTaskListQueryDTO queryDTO) {
        if (queryDTO.getCurrentActivityCode() == null) {
            return new HashMap<>();
        }

        // 使用XML方式查询所有符合条件的记录
        List<String> allInstances = mtcRepairTaskMapper.selectActivityCurrentStatusCodeListByCondition(
                queryDTO, Objects.requireNonNull(SessionUtils.getTenantId()).intValue());

        // 在内存中进行分组统计
        Map<String, Long> resultMap = new HashMap<>();
        for (String statusCode : allInstances) {
            resultMap.put(statusCode, resultMap.getOrDefault(statusCode, 0L) + 1);
        }

        return resultMap;
    }

    @Override
    public List<VehicleRepairRecordVO> queryVehicleRepairRecord(VehicleRepairRecordQueryDTO queryDTO) {
        SelectStatementProvider selectStatementProvider = select(mtcRepairTask.id,
                mtcRepairTask.taskNo,
                mtcRepairTask.repairTypeId,
                mtcRepairTask.repairTypeName,
                mtcRepairTask.repairTypeName,
                mtcRepairTask.vehicleInsuranceTotalAmount,
                mtcRepairTask.taskInflowTime,
                mtcRepairTask.taskCreateTime,
                mtcRepairTask.vehicleCheckTime,
                mtcRepairTask.totalMileage)
                .from(mtcRepairTask)
                .where()
                .and(mtcRepairTask.id, isNotEqualTo(queryDTO.getId()))
                .and(mtcRepairTask.vin, isEqualTo(queryDTO.getVin()))
                .and(mtcRepairTask.repairTypeId, isEqualToWhenPresent(queryDTO.getRepairTypeId()))
                .and(mtcRepairTask.repairDepotName,
                        isLikeWhenPresent(transFuzzyQueryParam(queryDTO.getRepairDepotName())))
                .and(mtcRepairTask.taskInflowTime,
                        isBetweenWhenPresent(queryDTO.getTaskInflowTimeStart())
                                .and(DateTimeUtils.addOneDay(queryDTO.getTaskInflowTimeEnd())))
                .and(mtcRepairTask.taskCreateTime,
                        isBetweenWhenPresent(queryDTO.getTaskCreateTimeStart())
                                .and(DateTimeUtils.addOneDay(queryDTO.getTaskCreateTimeEnd())))
                .and(mtcRepairTask.vehicleCheckTime,
                        isBetweenWhenPresent(queryDTO.getVehicleCheckDayStart())
                                .and(DateTimeUtils.addOneDay(queryDTO.getVehicleCheckDayEnd())))
                .and(mtcRepairTask.vehicleCheckTime, isNotNull().filter(
                        () -> null != queryDTO.getVehicleCheckDayStart() || null != queryDTO.getVehicleCheckDayEnd()))
                .orderBy(mtcRepairTask.id.descending())
                .build().render(RenderingStrategies.MYBATIS3);
        return mtcRepairTaskMapper.queryVehicleRepairRecord(selectStatementProvider);
    }

    @Override
    public Map<String, Long> countByActivityCode(RepairTaskListQueryDTO queryDTO) {
        // 直接查询所有符合条件的记录
        SelectStatementProvider selectStatementProvider = select(
                workflowInstance.currentActivityCode, 
                mtcRepairTask.advancedAuditLevel)
                .from(mtcRepairTask)
                .leftJoin(workflowInstance).on(workflowInstance.businessId, equalTo(mtcRepairTask.taskNo))
                .where()
                .and(workflowInstance.currentActivityCode, isEqualToWhenPresent(queryDTO.getCurrentActivityCode()).filter(StringUtils::isNotBlank))
                .and(workflowInstance.statusCode, isEqualToWhenPresent(queryDTO.getStatusCode()).filter(StringUtils::isNotBlank))
                .and(workflowInstance.statusCode, isNotIn(Arrays.asList(ActivityStatusEnum.COMPLETED.getCode(), ActivityStatusEnum.CLOSED.getCode())))
                .and(workflowInstance.tenantId, isEqualTo(SessionUtils.getTenantId().intValue()))
                .and(mtcRepairTask.vin, isEqualToWhenPresent(queryDTO.getVin()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.vehicleNo, isEqualToWhenPresent(queryDTO.getVehicleNo()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.taskNo, isEqualToWhenPresent(queryDTO.getTaskNo()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.repairTypeId, isEqualToWhenPresent(queryDTO.getRepairTypeId()))
                .and(mtcRepairTask.repairDepotId, isEqualToWhenPresent(queryDTO.getRepairDepotId()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.repairDepotOrgId, isEqualToWhenPresent(queryDTO.getRepairDepotOrgId()))
                .and(mtcRepairTask.vehicleModelSeq, isEqualToWhenPresent(queryDTO.getVehicleModelId()))
                .and(mtcRepairTask.renttype, isEqualToWhenPresent(queryDTO.getRenttype()))
                .and(mtcRepairTask.orgId, isEqualTo(queryDTO.getOrgId()).filter(StringUtils::isNotBlank))
                .and(mtcRepairTask.orgId, isInWhenPresent(queryDTO.getLoginOrgIds()))
                .and(mtcRepairTask.taskCreateTime,
                        isBetweenWhenPresent(queryDTO.getTaskCreateStartTime())
                                .and(DateTimeUtils.addOneDay(queryDTO.getTaskCreateEndTime())))
                .and(mtcRepairTask.taskInflowTime,
                        isBetweenWhenPresent(queryDTO.getTaskInflowStartTime())
                                .and(DateTimeUtils.addOneDay(queryDTO.getTaskInflowEndTime())))
                .and(mtcRepairTask.vehicleReciveTime,
                        isBetweenWhenPresent(queryDTO.getVehicleReciveStartTime())
                                .and(DateTimeUtils.addOneDay(queryDTO.getVehicleReciveEndTime())))
                .and(mtcRepairTask.vehicleCheckTime,
                        isBetweenWhenPresent(queryDTO.getVehicleCheckStartTime())
                                .and(DateTimeUtils.addOneDay(queryDTO.getVehicleCheckEndTime())))
                .build().render(RenderingStrategies.MYBATIS3);

        List<ActivityCodeResultDTO> allInstances = mtcRepairTaskMapper.selectActivityCodeList(selectStatementProvider);

        // 在内存中进行分组统计
        Map<String, Long> resultMap = new HashMap<>();
        for (ActivityCodeResultDTO dto : allInstances) {
            if (dto.getCurrentActivityCode().equals(ActivityDefinitionEnum.LOSS_ASSESSMENT.getCode())) {
                if (!dto.getAdvancedAuditLevel().equals(queryDTO.getAdvancedAuditLevel())) {
                    continue;
                }
            }
      
            resultMap.put(dto.getCurrentActivityCode(), resultMap.getOrDefault(dto.getCurrentActivityCode(), 0L) + 1);
        }

        return resultMap;
    }

    @Override
    public int adjustCustAmount(RepairCustInfoDTO repairCustInfoDTO) {
        MtcRepairTask updateMtcRepairTask = new MtcRepairTask();
        updateMtcRepairTask.setId(repairCustInfoDTO.getId());
        updateMtcRepairTask.setCustPaysDirect(repairCustInfoDTO.getCustPaysDirect());
        updateMtcRepairTask.setCustAmount(repairCustInfoDTO.getCustAmount());
        updateMtcRepairTask.setUserAssumedAmount(repairCustInfoDTO.getUserAssumedAmount());
        updateMtcRepairTask.setNotUserAssumedAmount(repairCustInfoDTO.getNotUserAssumedAmount());
        return updateSelectiveById(updateMtcRepairTask);
    }

    @Override
    public int updateSelfFundedAmount(Long id, BigDecimal selfFundedAmount) {
        MtcRepairTask updateMtcRepairTask = new MtcRepairTask();
        updateMtcRepairTask.setId(id);
        updateMtcRepairTask.setSelfFundedAmount(selfFundedAmount);
        return updateSelectiveById(updateMtcRepairTask);
    }
}
